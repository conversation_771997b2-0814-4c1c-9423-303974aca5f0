#!/usr/bin/env python3
"""
Comprehensive Installation Script for Medivent Pharmaceuticals ERP System
Automatically detects OS, installs dependencies, and sets up the environment
"""

import os
import sys
import subprocess
import platform
import json
import urllib.request
import shutil
import time
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class ERPInstaller:
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.current_dir = Path.cwd()
        self.requirements_file = self.current_dir / "requirements.txt"
        self.errors = []
        self.warnings = []
        
    def print_header(self):
        """Print installation header"""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 80)
        print("    MEDIVENT PHARMACEUTICALS ERP SYSTEM")
        print("         COMPREHENSIVE INSTALLER")
        print("=" * 80)
        print(f"{Colors.ENDC}")
        print(f"{Colors.OKCYAN}🏥 Welcome to the Medivent ERP Installation Wizard{Colors.ENDC}")
        print(f"{Colors.OKBLUE}📋 This installer will set up your complete ERP system{Colors.ENDC}")
        print()
        
    def check_system_info(self):
        """Check and display system information"""
        print(f"{Colors.BOLD}🔍 SYSTEM INFORMATION{Colors.ENDC}")
        print("-" * 40)
        print(f"Operating System: {Colors.OKGREEN}{platform.system()} {platform.release()}{Colors.ENDC}")
        print(f"Architecture: {Colors.OKGREEN}{platform.machine()}{Colors.ENDC}")
        print(f"Python Version: {Colors.OKGREEN}{sys.version.split()[0]}{Colors.ENDC}")
        print(f"Python Executable: {Colors.OKGREEN}{sys.executable}{Colors.ENDC}")
        print(f"Current Directory: {Colors.OKGREEN}{self.current_dir}{Colors.ENDC}")
        print()
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        print(f"{Colors.BOLD}🐍 PYTHON VERSION CHECK{Colors.ENDC}")
        print("-" * 40)
        
        min_version = (3, 7)
        if self.python_version < min_version:
            self.errors.append(f"Python {min_version[0]}.{min_version[1]}+ required, found {self.python_version[0]}.{self.python_version[1]}")
            print(f"{Colors.FAIL}❌ Python version too old: {self.python_version[0]}.{self.python_version[1]}{Colors.ENDC}")
            return False
        else:
            print(f"{Colors.OKGREEN}✅ Python version compatible: {self.python_version[0]}.{self.python_version[1]}.{self.python_version[2]}{Colors.ENDC}")
            return True
            
    def check_pip(self):
        """Check if pip is available and upgrade if needed"""
        print(f"{Colors.BOLD}📦 PIP CHECK AND UPGRADE{Colors.ENDC}")
        print("-" * 40)
        
        try:
            import pip
            print(f"{Colors.OKGREEN}✅ pip is available{Colors.ENDC}")
        except ImportError:
            print(f"{Colors.FAIL}❌ pip not found{Colors.ENDC}")
            self.errors.append("pip is not installed")
            return False
            
        # Upgrade pip
        try:
            print(f"{Colors.OKCYAN}🔄 Upgrading pip...{Colors.ENDC}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"{Colors.OKGREEN}✅ pip upgraded successfully{Colors.ENDC}")
        except subprocess.CalledProcessError:
            self.warnings.append("Could not upgrade pip")
            print(f"{Colors.WARNING}⚠️ Could not upgrade pip (continuing anyway){Colors.ENDC}")
            
        return True
        
    def check_requirements_file(self):
        """Check if requirements.txt exists"""
        print(f"{Colors.BOLD}📄 REQUIREMENTS FILE CHECK{Colors.ENDC}")
        print("-" * 40)
        
        if not self.requirements_file.exists():
            self.errors.append("requirements.txt not found")
            print(f"{Colors.FAIL}❌ requirements.txt not found in {self.current_dir}{Colors.ENDC}")
            return False
        else:
            print(f"{Colors.OKGREEN}✅ requirements.txt found{Colors.ENDC}")
            
            # Display requirements
            with open(self.requirements_file, 'r') as f:
                lines = f.readlines()
                package_count = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
                print(f"{Colors.OKBLUE}📋 Found {package_count} packages to install{Colors.ENDC}")
            return True
            
    def install_system_dependencies(self):
        """Install system-level dependencies based on OS"""
        print(f"{Colors.BOLD}🔧 SYSTEM DEPENDENCIES{Colors.ENDC}")
        print("-" * 40)
        
        if self.system == "linux":
            self._install_linux_dependencies()
        elif self.system == "darwin":  # macOS
            self._install_macos_dependencies()
        elif self.system == "windows":
            self._install_windows_dependencies()
        else:
            print(f"{Colors.WARNING}⚠️ Unknown operating system: {self.system}{Colors.ENDC}")
            self.warnings.append(f"Unknown operating system: {self.system}")
            
    def _install_linux_dependencies(self):
        """Install Linux system dependencies"""
        print(f"{Colors.OKCYAN}🐧 Installing Linux dependencies...{Colors.ENDC}")
        
        # Check if we can use apt (Debian/Ubuntu)
        if shutil.which("apt-get"):
            try:
                print("📦 Updating package list...")
                subprocess.run(["sudo", "apt-get", "update"], check=True, 
                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                packages = [
                    "python3-dev",
                    "python3-pip", 
                    "build-essential",
                    "libsqlite3-dev",
                    "libjpeg-dev",
                    "libpng-dev",
                    "libfreetype6-dev",
                    "pkg-config"
                ]
                
                print(f"📦 Installing packages: {', '.join(packages)}")
                subprocess.run(["sudo", "apt-get", "install", "-y"] + packages, 
                             check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"{Colors.OKGREEN}✅ Linux dependencies installed{Colors.ENDC}")
                
            except subprocess.CalledProcessError as e:
                self.warnings.append(f"Could not install some Linux dependencies: {e}")
                print(f"{Colors.WARNING}⚠️ Some Linux dependencies may not have installed correctly{Colors.ENDC}")
                
        # Check if we can use yum (RHEL/CentOS)
        elif shutil.which("yum"):
            try:
                packages = [
                    "python3-devel",
                    "gcc",
                    "sqlite-devel",
                    "libjpeg-devel",
                    "libpng-devel",
                    "freetype-devel"
                ]
                
                print(f"📦 Installing packages: {', '.join(packages)}")
                subprocess.run(["sudo", "yum", "install", "-y"] + packages,
                             check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"{Colors.OKGREEN}✅ Linux dependencies installed{Colors.ENDC}")
                
            except subprocess.CalledProcessError as e:
                self.warnings.append(f"Could not install some Linux dependencies: {e}")
                print(f"{Colors.WARNING}⚠️ Some Linux dependencies may not have installed correctly{Colors.ENDC}")
        else:
            print(f"{Colors.WARNING}⚠️ No supported package manager found (apt-get or yum){Colors.ENDC}")
            self.warnings.append("No supported package manager found")
            
    def _install_macos_dependencies(self):
        """Install macOS system dependencies"""
        print(f"{Colors.OKCYAN}🍎 Installing macOS dependencies...{Colors.ENDC}")
        
        # Check if Homebrew is available
        if shutil.which("brew"):
            try:
                packages = [
                    "sqlite3",
                    "jpeg",
                    "libpng",
                    "freetype",
                    "pkg-config"
                ]
                
                print(f"📦 Installing packages via Homebrew: {', '.join(packages)}")
                subprocess.run(["brew", "install"] + packages,
                             check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"{Colors.OKGREEN}✅ macOS dependencies installed{Colors.ENDC}")
                
            except subprocess.CalledProcessError as e:
                self.warnings.append(f"Could not install some macOS dependencies: {e}")
                print(f"{Colors.WARNING}⚠️ Some macOS dependencies may not have installed correctly{Colors.ENDC}")
        else:
            print(f"{Colors.WARNING}⚠️ Homebrew not found. Please install Homebrew first: https://brew.sh{Colors.ENDC}")
            self.warnings.append("Homebrew not found")
            
    def _install_windows_dependencies(self):
        """Install Windows system dependencies"""
        print(f"{Colors.OKCYAN}🪟 Checking Windows dependencies...{Colors.ENDC}")
        print(f"{Colors.OKGREEN}✅ Windows dependencies are handled by pip packages{Colors.ENDC}")
        
    def install_python_packages(self):
        """Install Python packages from requirements.txt"""
        print(f"{Colors.BOLD}🐍 PYTHON PACKAGES INSTALLATION{Colors.ENDC}")
        print("-" * 40)
        
        try:
            print(f"{Colors.OKCYAN}📦 Installing packages from requirements.txt...{Colors.ENDC}")
            print("This may take several minutes...")
            
            # Install packages with progress indication
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file),
                "--upgrade", "--no-cache-dir"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"{Colors.OKGREEN}✅ All Python packages installed successfully{Colors.ENDC}")
                return True
            else:
                print(f"{Colors.FAIL}❌ Package installation failed{Colors.ENDC}")
                print(f"{Colors.FAIL}Error output:{Colors.ENDC}")
                print(result.stderr)
                self.errors.append("Python package installation failed")
                return False
                
        except Exception as e:
            print(f"{Colors.FAIL}❌ Exception during package installation: {e}{Colors.ENDC}")
            self.errors.append(f"Exception during package installation: {e}")
            return False
            
    def verify_installation(self):
        """Verify that all required packages are installed"""
        print(f"{Colors.BOLD}🔍 INSTALLATION VERIFICATION{Colors.ENDC}")
        print("-" * 40)
        
        required_packages = [
            'flask', 'pandas', 'matplotlib', 'plotly', 'seaborn',
            'reportlab', 'pillow', 'requests', 'bleach', 'openpyxl'
        ]
        
        failed_imports = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"{Colors.OKGREEN}✅ {package}{Colors.ENDC}")
            except ImportError:
                print(f"{Colors.FAIL}❌ {package}{Colors.ENDC}")
                failed_imports.append(package)
                
        if failed_imports:
            print(f"{Colors.FAIL}❌ Some packages failed to import: {', '.join(failed_imports)}{Colors.ENDC}")
            self.errors.append(f"Failed to import: {', '.join(failed_imports)}")
            return False
        else:
            print(f"{Colors.OKGREEN}✅ All required packages verified{Colors.ENDC}")
            return True

    def setup_database(self):
        """Set up the database if needed"""
        print(f"{Colors.BOLD}🗄️ DATABASE SETUP{Colors.ENDC}")
        print("-" * 40)

        db_path = self.current_dir / "instance" / "medivent.db"

        if db_path.exists():
            print(f"{Colors.OKGREEN}✅ Database already exists at {db_path}{Colors.ENDC}")
            return True
        else:
            print(f"{Colors.OKCYAN}🔄 Database not found, will be created on first run{Colors.ENDC}")

            # Create instance directory if it doesn't exist
            instance_dir = self.current_dir / "instance"
            instance_dir.mkdir(exist_ok=True)
            print(f"{Colors.OKGREEN}✅ Instance directory created{Colors.ENDC}")
            return True

    def create_startup_scripts(self):
        """Create or verify startup scripts"""
        print(f"{Colors.BOLD}🚀 STARTUP SCRIPTS{Colors.ENDC}")
        print("-" * 40)

        # Check if startup scripts exist
        bat_script = self.current_dir / "start_erp.bat"
        sh_script = self.current_dir / "start_erp.sh"

        if bat_script.exists():
            print(f"{Colors.OKGREEN}✅ Windows startup script found: {bat_script.name}{Colors.ENDC}")
        else:
            print(f"{Colors.WARNING}⚠️ Windows startup script not found{Colors.ENDC}")

        if sh_script.exists():
            print(f"{Colors.OKGREEN}✅ Unix startup script found: {sh_script.name}{Colors.ENDC}")
        else:
            print(f"{Colors.WARNING}⚠️ Unix startup script not found{Colors.ENDC}")

        return True

    def test_application(self):
        """Test if the application can start"""
        print(f"{Colors.BOLD}🧪 APPLICATION TEST{Colors.ENDC}")
        print("-" * 40)

        try:
            print(f"{Colors.OKCYAN}🔄 Testing application import...{Colors.ENDC}")

            # Try to import the main app module
            import app
            print(f"{Colors.OKGREEN}✅ Application module imported successfully{Colors.ENDC}")

            # Check if Flask app is created
            if hasattr(app, 'app'):
                print(f"{Colors.OKGREEN}✅ Flask application instance found{Colors.ENDC}")
                return True
            else:
                print(f"{Colors.FAIL}❌ Flask application instance not found{Colors.ENDC}")
                self.errors.append("Flask application instance not found")
                return False

        except ImportError as e:
            print(f"{Colors.FAIL}❌ Could not import application: {e}{Colors.ENDC}")
            self.errors.append(f"Could not import application: {e}")
            return False
        except Exception as e:
            print(f"{Colors.FAIL}❌ Application test failed: {e}{Colors.ENDC}")
            self.errors.append(f"Application test failed: {e}")
            return False

    def print_summary(self):
        """Print installation summary"""
        print()
        print(f"{Colors.BOLD}📋 INSTALLATION SUMMARY{Colors.ENDC}")
        print("=" * 60)

        if not self.errors:
            print(f"{Colors.OKGREEN}{Colors.BOLD}🎉 INSTALLATION SUCCESSFUL!{Colors.ENDC}")
            print()
            print(f"{Colors.OKGREEN}✅ All components installed successfully{Colors.ENDC}")

            if self.warnings:
                print(f"{Colors.WARNING}⚠️ Warnings ({len(self.warnings)}):{Colors.ENDC}")
                for warning in self.warnings:
                    print(f"   • {warning}")
                print()

            print(f"{Colors.BOLD}🚀 NEXT STEPS:{Colors.ENDC}")
            print(f"1. Start the application:")
            if self.system == "windows":
                print(f"   {Colors.OKCYAN}start_erp.bat{Colors.ENDC}")
            else:
                print(f"   {Colors.OKCYAN}./start_erp.sh{Colors.ENDC}")
                print(f"   or")
                print(f"   {Colors.OKCYAN}python run_app.py{Colors.ENDC}")

            print(f"2. Open your browser to: {Colors.OKCYAN}http://localhost:3000{Colors.ENDC}")
            print(f"3. Login with: {Colors.OKCYAN}admin / admin123{Colors.ENDC}")
            print()
            print(f"{Colors.OKGREEN}🏥 Welcome to Medivent Pharmaceuticals ERP System!{Colors.ENDC}")

        else:
            print(f"{Colors.FAIL}{Colors.BOLD}❌ INSTALLATION FAILED{Colors.ENDC}")
            print()
            print(f"{Colors.FAIL}Errors ({len(self.errors)}):{Colors.ENDC}")
            for error in self.errors:
                print(f"   • {error}")

            if self.warnings:
                print(f"{Colors.WARNING}Warnings ({len(self.warnings)}):{Colors.ENDC}")
                for warning in self.warnings:
                    print(f"   • {warning}")

            print()
            print(f"{Colors.BOLD}🔧 TROUBLESHOOTING:{Colors.ENDC}")
            print(f"1. Check Python version (3.7+ required)")
            print(f"2. Ensure pip is installed and working")
            print(f"3. Check internet connection for package downloads")
            print(f"4. Run with administrator/sudo privileges if needed")
            print(f"5. Check the error messages above for specific issues")

    def run_installation(self):
        """Run the complete installation process"""
        self.print_header()

        # System checks
        self.check_system_info()

        if not self.check_python_version():
            self.print_summary()
            return False

        if not self.check_pip():
            self.print_summary()
            return False

        if not self.check_requirements_file():
            self.print_summary()
            return False

        # Install dependencies
        self.install_system_dependencies()

        if not self.install_python_packages():
            self.print_summary()
            return False

        # Verification and setup
        if not self.verify_installation():
            self.print_summary()
            return False

        self.setup_database()
        self.create_startup_scripts()

        if not self.test_application():
            self.print_summary()
            return False

        self.print_summary()
        return True

def main():
    """Main installation function"""
    installer = ERPInstaller()

    try:
        success = installer.run_installation()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️ Installation cancelled by user{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Unexpected error: {e}{Colors.ENDC}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
