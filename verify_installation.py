#!/usr/bin/env python3
"""
Installation Verification Script for Medivent Pharmaceuticals ERP System
Verifies all dependencies and system requirements are met
"""

import os
import sys
import subprocess
import platform
import sqlite3
import importlib
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

class InstallationVerifier:
    def __init__(self):
        self.current_dir = Path.cwd()
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
        
    def print_header(self):
        """Print verification header"""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 70)
        print("    MEDIVENT ERP INSTALLATION VERIFICATION")
        print("=" * 70)
        print(f"{Colors.ENDC}")
        print(f"{Colors.OKCYAN}🔍 Verifying system readiness...{Colors.ENDC}")
        print()
        
    def check_python_version(self):
        """Verify Python version"""
        print(f"{Colors.BOLD}🐍 Python Version Check{Colors.ENDC}")
        print("-" * 30)
        
        self.total_checks += 1
        version = sys.version_info
        min_version = (3, 7)
        
        if version >= min_version:
            print(f"{Colors.OKGREEN}✅ Python {version.major}.{version.minor}.{version.micro} (Required: 3.7+){Colors.ENDC}")
            self.success_count += 1
            return True
        else:
            print(f"{Colors.FAIL}❌ Python {version.major}.{version.minor}.{version.micro} (Required: 3.7+){Colors.ENDC}")
            self.errors.append(f"Python version too old: {version.major}.{version.minor}")
            return False
            
    def check_required_files(self):
        """Check if all required files exist"""
        print(f"\n{Colors.BOLD}📁 Required Files Check{Colors.ENDC}")
        print("-" * 30)
        
        required_files = [
            "app.py",
            "requirements.txt",
            "schema.sql",
            "run_app.py",
            "start_erp.bat",
            "start_erp.sh"
        ]
        
        all_found = True
        
        for file_name in required_files:
            self.total_checks += 1
            file_path = self.current_dir / file_name
            
            if file_path.exists():
                print(f"{Colors.OKGREEN}✅ {file_name}{Colors.ENDC}")
                self.success_count += 1
            else:
                print(f"{Colors.FAIL}❌ {file_name}{Colors.ENDC}")
                self.errors.append(f"Missing file: {file_name}")
                all_found = False
                
        return all_found
        
    def check_directories(self):
        """Check if required directories exist"""
        print(f"\n{Colors.BOLD}📂 Required Directories Check{Colors.ENDC}")
        print("-" * 30)
        
        required_dirs = [
            "templates",
            "static",
            "routes",
            "utils",
            "instance"
        ]
        
        all_found = True
        
        for dir_name in required_dirs:
            self.total_checks += 1
            dir_path = self.current_dir / dir_name
            
            if dir_path.exists() and dir_path.is_dir():
                print(f"{Colors.OKGREEN}✅ {dir_name}//{Colors.ENDC}")
                self.success_count += 1
            else:
                print(f"{Colors.FAIL}❌ {dir_name}//{Colors.ENDC}")
                self.errors.append(f"Missing directory: {dir_name}")
                all_found = False
                
        return all_found
        
    def check_python_packages(self):
        """Verify all required Python packages are installed"""
        print(f"\n{Colors.BOLD}📦 Python Packages Check{Colors.ENDC}")
        print("-" * 30)
        
        # Core packages from requirements.txt
        required_packages = {
            'flask': 'Flask==2.3.3',
            'flask_sqlalchemy': 'Flask-SQLAlchemy==3.1.1',
            'flask_login': 'Flask-Login==0.6.2',
            'flask_wtf': 'Flask-WTF==1.2.1',
            'werkzeug': 'Werkzeug==2.3.7',
            'sqlalchemy': 'SQLAlchemy==2.0.23',
            'jinja2': 'Jinja2==3.1.2',
            'wtforms': 'WTForms==3.1.1',
            'pandas': 'pandas==2.2.3',
            'numpy': 'numpy==2.2.5',
            'xlsxwriter': 'xlsxwriter==3.1.9',
            'matplotlib': 'matplotlib==3.10.1',
            'plotly': 'plotly==5.17.0',
            'seaborn': 'seaborn==0.13.0',
            'reportlab': 'reportlab==4.0.7',
            'PIL': 'pillow==11.2.1',
            'requests': 'requests==2.32.3',
            'bleach': 'bleach==6.1.0',
            'openpyxl': 'openpyxl==3.1.2'
        }
        
        all_installed = True
        
        for package, version_info in required_packages.items():
            self.total_checks += 1
            
            try:
                module = importlib.import_module(package)
                
                # Try to get version
                version = getattr(module, '__version__', 'Unknown')
                print(f"{Colors.OKGREEN}✅ {package} ({version}){Colors.ENDC}")
                self.success_count += 1
                
            except ImportError:
                print(f"{Colors.FAIL}❌ {package} - NOT INSTALLED{Colors.ENDC}")
                self.errors.append(f"Missing package: {package}")
                all_installed = False
                
        return all_installed
        
    def check_database(self):
        """Check database connectivity and structure"""
        print(f"\n{Colors.BOLD}🗄️ Database Check{Colors.ENDC}")
        print("-" * 30)
        
        db_path = self.current_dir / "instance" / "medivent.db"
        
        self.total_checks += 1
        
        if not db_path.exists():
            print(f"{Colors.WARNING}⚠️ Database file not found (will be created on first run){Colors.ENDC}")
            self.warnings.append("Database file not found")
            self.success_count += 1
            return True
            
        try:
            # Test database connection
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Check if key tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'products', 'orders', 'inventory', 'warehouses']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print(f"{Colors.WARNING}⚠️ Database missing tables: {', '.join(missing_tables)}{Colors.ENDC}")
                self.warnings.append(f"Missing database tables: {', '.join(missing_tables)}")
            else:
                print(f"{Colors.OKGREEN}✅ Database structure verified ({len(tables)} tables){Colors.ENDC}")
                
            conn.close()
            self.success_count += 1
            return True
            
        except Exception as e:
            print(f"{Colors.FAIL}❌ Database error: {e}{Colors.ENDC}")
            self.errors.append(f"Database error: {e}")
            return False
            
    def check_application_import(self):
        """Test if the main application can be imported"""
        print(f"\n{Colors.BOLD}🚀 Application Import Test{Colors.ENDC}")
        print("-" * 30)
        
        self.total_checks += 1
        
        try:
            # Add current directory to path
            sys.path.insert(0, str(self.current_dir))
            
            # Try to import the main app
            import app
            
            if hasattr(app, 'app'):
                print(f"{Colors.OKGREEN}✅ Flask application imported successfully{Colors.ENDC}")
                self.success_count += 1
                return True
            else:
                print(f"{Colors.FAIL}❌ Flask app instance not found{Colors.ENDC}")
                self.errors.append("Flask app instance not found")
                return False
                
        except ImportError as e:
            print(f"{Colors.FAIL}❌ Import error: {e}{Colors.ENDC}")
            self.errors.append(f"Import error: {e}")
            return False
        except Exception as e:
            print(f"{Colors.FAIL}❌ Application error: {e}{Colors.ENDC}")
            self.errors.append(f"Application error: {e}")
            return False
            
    def check_system_requirements(self):
        """Check system-level requirements"""
        print(f"\n{Colors.BOLD}💻 System Requirements{Colors.ENDC}")
        print("-" * 30)
        
        # Check available memory (basic check)
        self.total_checks += 1
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            
            if memory_gb >= 1:
                print(f"{Colors.OKGREEN}✅ Memory: {memory_gb:.1f} GB (Required: 1+ GB){Colors.ENDC}")
                self.success_count += 1
            else:
                print(f"{Colors.WARNING}⚠️ Memory: {memory_gb:.1f} GB (Recommended: 1+ GB){Colors.ENDC}")
                self.warnings.append("Low memory")
                self.success_count += 1
                
        except ImportError:
            print(f"{Colors.OKBLUE}ℹ️ Memory check skipped (psutil not available){Colors.ENDC}")
            self.success_count += 1
            
        # Check disk space
        self.total_checks += 1
        try:
            disk_usage = os.statvfs(str(self.current_dir))
            free_space_gb = (disk_usage.f_frsize * disk_usage.f_bavail) / (1024**3)
            
            if free_space_gb >= 0.5:
                print(f"{Colors.OKGREEN}✅ Disk space: {free_space_gb:.1f} GB free{Colors.ENDC}")
                self.success_count += 1
            else:
                print(f"{Colors.WARNING}⚠️ Disk space: {free_space_gb:.1f} GB free (Low){Colors.ENDC}")
                self.warnings.append("Low disk space")
                self.success_count += 1
                
        except (AttributeError, OSError):
            print(f"{Colors.OKBLUE}ℹ️ Disk space check skipped (not available on this OS){Colors.ENDC}")
            self.success_count += 1
            
        return True

    def print_summary(self):
        """Print verification summary"""
        print(f"\n{Colors.BOLD}📋 VERIFICATION SUMMARY{Colors.ENDC}")
        print("=" * 50)

        success_rate = (self.success_count / self.total_checks * 100) if self.total_checks > 0 else 0

        print(f"Total Checks: {self.total_checks}")
        print(f"Successful: {Colors.OKGREEN}{self.success_count}{Colors.ENDC}")
        print(f"Errors: {Colors.FAIL}{len(self.errors)}{Colors.ENDC}")
        print(f"Warnings: {Colors.WARNING}{len(self.warnings)}{Colors.ENDC}")
        print(f"Success Rate: {Colors.OKGREEN if success_rate >= 90 else Colors.WARNING if success_rate >= 70 else Colors.FAIL}{success_rate:.1f}%{Colors.ENDC}")
        print()

        if self.errors:
            print(f"{Colors.FAIL}{Colors.BOLD}❌ ERRORS FOUND:{Colors.ENDC}")
            for error in self.errors:
                print(f"   • {error}")
            print()

        if self.warnings:
            print(f"{Colors.WARNING}{Colors.BOLD}⚠️ WARNINGS:{Colors.ENDC}")
            for warning in self.warnings:
                print(f"   • {warning}")
            print()

        if not self.errors:
            print(f"{Colors.OKGREEN}{Colors.BOLD}🎉 SYSTEM READY!{Colors.ENDC}")
            print(f"{Colors.OKGREEN}Your Medivent ERP system is ready to run.{Colors.ENDC}")
            print()
            print(f"{Colors.BOLD}🚀 TO START THE SYSTEM:{Colors.ENDC}")
            if platform.system().lower() == "windows":
                print(f"   {Colors.OKCYAN}start_erp.bat{Colors.ENDC}")
            else:
                print(f"   {Colors.OKCYAN}./start_erp.sh{Colors.ENDC}")
                print(f"   or")
                print(f"   {Colors.OKCYAN}python run_app.py{Colors.ENDC}")
            print()
            print(f"Then open: {Colors.OKCYAN}http://localhost:3000{Colors.ENDC}")
            print(f"Login: {Colors.OKCYAN}admin / admin123{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}{Colors.BOLD}❌ SYSTEM NOT READY{Colors.ENDC}")
            print(f"{Colors.FAIL}Please fix the errors above before running the system.{Colors.ENDC}")
            print()
            print(f"{Colors.BOLD}🔧 SUGGESTED ACTIONS:{Colors.ENDC}")
            print(f"1. Run the installation script: {Colors.OKCYAN}python install_erp.py{Colors.ENDC}")
            print(f"2. Install missing packages: {Colors.OKCYAN}pip install -r requirements.txt{Colors.ENDC}")
            print(f"3. Check file permissions and paths")

    def run_verification(self):
        """Run all verification checks"""
        self.print_header()

        # Run all checks
        self.check_python_version()
        self.check_required_files()
        self.check_directories()
        self.check_python_packages()
        self.check_database()
        self.check_application_import()
        self.check_system_requirements()

        # Print summary
        self.print_summary()

        # Return success status
        return len(self.errors) == 0

def main():
    """Main verification function"""
    verifier = InstallationVerifier()

    try:
        success = verifier.run_verification()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️ Verification cancelled by user{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Unexpected error: {e}{Colors.ENDC}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
