"""
PDF Invoice Generator for Medivent Pharmaceuticals Web Portal
"""

import os
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm, inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, Flowable, KeepTogether
from reportlab.graphics.shapes import Drawing, Line
from reportlab.pdfgen import canvas

# Company details - These will be loaded from database settings
COMPANY_NAME = "Your Company Name"
COMPANY_ADDRESS = "Your Company Address"
COMPANY_PHONE = "Your Phone Number"
COMPANY_EMAIL = "<EMAIL>"
COMPANY_NTN = "Your NTN Number"

# Define custom colors
MAIN_COLOR = colors.HexColor('#00AEEF')  # Blue color
LIGHT_BLUE = colors.HexColor('#E6F7FF')  # Light blue for backgrounds
DARK_GRAY = colors.HexColor('#333333')   # Dark gray for text
DISCOUNT_RED = colors.HexColor('#FF0000')  # Red color for discounts

# Custom flowable for horizontal line
class HRFlowable(Flowable):
    """
    Horizontal Rule flowable --- draws a line across the page
    """
    def __init__(self, width, color=colors.black, thickness=1):
        Flowable.__init__(self)
        self.width = width
        self.color = color
        self.thickness = thickness

    def draw(self):
        self.canv.setStrokeColor(self.color)
        self.canv.setLineWidth(self.thickness)
        self.canv.line(0, 0, self.width, 0)

def generate_pdf_invoice(order, invoice_number, username):
    """
    Generate a professional PDF invoice for an order

    Args:
        order: The order object
        invoice_number: The invoice number
        username: The user generating the invoice

    Returns:
        str: Path to the generated PDF file if successful, None otherwise
    """
    try:
        # Create invoices directory if it doesn't exist
        static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
        invoices_dir = os.path.join(static_dir, 'documents', 'invoices')
        os.makedirs(invoices_dir, exist_ok=True)

        # PDF file path
        pdf_path = os.path.join(invoices_dir, f"{invoice_number}.pdf")

        # Create the PDF document
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm
        )

        # Container for the 'Flowable' objects
        elements = []

        # Styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='CompanyName',
            fontName='Helvetica-Bold',
            fontSize=16,
            alignment=1,  # Center alignment
            textColor=MAIN_COLOR
        ))
        styles.add(ParagraphStyle(
            name='InvoiceTitle',
            fontName='Helvetica-Bold',
            fontSize=24,
            alignment=1,  # Center alignment
            textColor=MAIN_COLOR,
            spaceAfter=0.5*cm
        ))
        styles.add(ParagraphStyle(
            name='SectionTitle',
            fontName='Helvetica-Bold',
            fontSize=12,
            textColor=MAIN_COLOR
        ))
        styles.add(ParagraphStyle(
            name='CustomerDetails',
            fontName='Helvetica',
            fontSize=10,
            leading=14
        ))
        styles.add(ParagraphStyle(
            name='TableHeader',
            fontName='Helvetica-Bold',
            fontSize=10,
            alignment=1  # Center alignment
        ))
        styles.add(ParagraphStyle(
            name='TableCell',
            fontName='Helvetica',
            fontSize=10,
            alignment=1  # Center alignment
        ))
        styles.add(ParagraphStyle(
            name='TableCellLeft',
            fontName='Helvetica',
            fontSize=10,
            alignment=0  # Left alignment
        ))
        styles.add(ParagraphStyle(
            name='TableCellRight',
            fontName='Helvetica',
            fontSize=10,
            alignment=2  # Right alignment
        ))
        styles.add(ParagraphStyle(
            name='Total',
            fontName='Helvetica-Bold',
            fontSize=12,
            alignment=2  # Right alignment
        ))
        styles.add(ParagraphStyle(
            name='Notes',
            fontName='Helvetica',
            fontSize=9,
            textColor=colors.gray
        ))

        # Current date
        current_date = datetime.now().strftime("%d/%m/%Y")

        # Company name and logo
        elements.append(Paragraph(COMPANY_NAME, styles['CompanyName']))
        elements.append(Spacer(1, 5))

        # Create styles for the header table
        left_style = ParagraphStyle(
            name='LeftAlign',
            parent=styles['Normal'],
            alignment=0  # Left alignment
        )
        center_style = ParagraphStyle(
            name='CenterAlign',
            parent=styles['Normal'],
            alignment=1  # Center alignment
        )
        right_style = ParagraphStyle(
            name='RightAlign',
            parent=styles['Normal'],
            alignment=2  # Right alignment
        )

        # Create the three-column table with date, invoice number, and address
        header_data = [
            # First row: Labels
            [Paragraph('<font color="#00AEEF"><b>DATE OF ISSUE</b></font>', left_style),
             Paragraph('<font color="#00AEEF"><b>INVOICE #</b></font>', center_style),
             Paragraph('', right_style)],

            # Second row: Values and first address line
            [Paragraph(f"{current_date}", left_style),
             Paragraph(f"{invoice_number}", center_style),
             Paragraph(f"Plot #25, Sector 14-B, North Industrial Zone", right_style)],

            # Third row: Empty, empty, second address line
            ["", "",
             Paragraph(f"Karachi, Sindh, Pakistan", right_style)],

            # Fourth row: Empty, empty, phone
            ["", "",
             Paragraph(f"Phone: {COMPANY_PHONE}", right_style)]
        ]

        # Create the table with specific column widths
        header_table = Table(header_data, colWidths=[doc.width/3.0]*3)
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('TOPPADDING', (0, 0), (-1, -1), 1),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 1),
        ]))

        elements.append(header_table)
        elements.append(Spacer(1, 15))

        # Horizontal line
        elements.append(HRFlowable(width=doc.width, color=MAIN_COLOR, thickness=1))
        elements.append(Spacer(1, 15))

        # Bill To section
        elements.append(Paragraph('<font color="#00AEEF"><b>BILL TO</b></font>', styles['SectionTitle']))
        elements.append(Spacer(1, 5))
        elements.append(Paragraph(f"<b>{order.customer_name}</b>", styles['CustomerDetails']))
        elements.append(Paragraph(f"{order.customer_address}", styles['CustomerDetails']))
        elements.append(Paragraph(f"Mobile: {order.customer_phone}", styles['CustomerDetails']))
        elements.append(Spacer(1, 20))

        # Get order items
        from models import OrderItem
        order_items = OrderItem.query.filter_by(order_id=order.order_id).all()

        # Create table for products
        product_data = [
            # Table header
            [Paragraph("<b>DESCRIPTION</b>", styles['TableCellLeft']),
             Paragraph("<b>UNIT PRICE</b>", styles['TableCellRight']),
             Paragraph("<b>QTY</b>", styles['TableCell']),
             Paragraph("<b>AMOUNT</b>", styles['TableCellRight'])]
        ]

        # Add product rows
        for item in order_items:
            product_data.append([
                Paragraph(f"{item.product_name} {item.strength}", styles['TableCellLeft']),
                Paragraph(f"Rs. {item.unit_price:.2f}", styles['TableCellRight']),
                Paragraph(f"{item.quantity}", styles['TableCell']),
                Paragraph(f"Rs. {item.line_total:.2f}", styles['TableCellRight'])
            ])

        # Create the table with specific column widths
        product_table = Table(product_data, colWidths=[doc.width*0.5, doc.width*0.2, doc.width*0.1, doc.width*0.2])
        product_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), LIGHT_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), DARK_GRAY),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        elements.append(product_table)
        elements.append(Spacer(1, 5))

        # Totals section
        # Calculate subtotal, tax, and total
        subtotal = order.order_amount
        tax_rate = 0.10  # 10% tax
        tax = subtotal * tax_rate
        total = subtotal + tax

        # Create table for totals
        totals_data = [
            ["", Paragraph("<b>SUBTOTAL</b>", styles['TableCellRight']), Paragraph(f"Rs. {subtotal:.2f}", styles['TableCellRight'])],
            ["", Paragraph("<b>TAX (10%)</b>", styles['TableCellRight']), Paragraph(f"Rs. {tax:.2f}", styles['TableCellRight'])],
            ["", Paragraph("<b>TOTAL</b>", styles['Total']), Paragraph(f"<b>Rs. {total:.2f}</b>", styles['Total'])]
        ]

        # Create the table with specific column widths
        totals_table = Table(totals_data, colWidths=[doc.width*0.6, doc.width*0.2, doc.width*0.2])
        totals_table.setStyle(TableStyle([
            ('LINEABOVE', (1, 2), (2, 2), 1, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 30))

        # Notes section
        elements.append(Paragraph("<b>NOTES</b>", styles['SectionTitle']))
        elements.append(Spacer(1, 5))
        elements.append(Paragraph("Thank you for your business. Payment is due within 30 days.", styles['Notes']))
        elements.append(Spacer(1, 5))
        elements.append(Paragraph(f"NTN: {COMPANY_NTN}", styles['Notes']))
        elements.append(Spacer(1, 5))
        elements.append(Paragraph("This is a computer generated invoice and does not require a signature.", styles['Notes']))

        # Build the PDF
        doc.build(elements)

        print(f"PDF Invoice generated successfully: {pdf_path}")
        return pdf_path

    except Exception as e:
        print(f"Error generating PDF invoice: {e}")
        return None
