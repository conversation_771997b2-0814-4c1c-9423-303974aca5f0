{% extends 'base.html' %}

{% block title %}Application Settings{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Application Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">Company Information</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('settings') }}">
                                        <div class="form-group">
                                            <label for="company_name">Company Name</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" value="{{ settings.get('company_name', 'Your Company Name') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="company_address">Company Address</label>
                                            <textarea class="form-control" id="company_address" name="company_address" rows="3" required>{{ settings.get('company_address', '') }}</textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="company_phone">Company Phone</label>
                                            <input type="text" class="form-control" id="company_phone" name="company_phone" value="{{ settings.get('company_phone', '') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="company_email">Company Email</label>
                                            <input type="email" class="form-control" id="company_email" name="company_email" value="{{ settings.get('company_email', '') }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="company_ntn">Company NTN</label>
                                            <input type="text" class="form-control" id="company_ntn" name="company_ntn" value="{{ settings.get('company_ntn', '') }}" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">Current Settings</h5>
                                </div>
                                <div class="card-body">
                                    {% if settings %}
                                        <table class="table table-bordered">
                                            <tr>
                                                <th>Company Name</th>
                                                <td>{{ settings.get('company_name', '') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Company Address</th>
                                                <td>{{ settings.get('company_address', '') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Company Phone</th>
                                                <td>{{ settings.get('company_phone', '') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Company Email</th>
                                                <td>{{ settings.get('company_email', '') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Company NTN</th>
                                                <td>{{ settings.get('company_ntn', '') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Last Updated</th>
                                                <td>{{ safe_strftime(now, '%Y-%m-%d %H:%M:%S') }}</td>
                                            </tr>
                                        </table>
                                    {% else %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i> No settings have been saved yet. Please fill out the form to save your company information.
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="card mt-4">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">System Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Application Version</th>
                                            <td>1.0.0</td>
                                        </tr>
                                        <tr>
                                            <th>Database Version</th>
                                            <td>1.0.0</td>
                                        </tr>
                                        <tr>
                                            <th>Last Database Backup</th>
                                            <td>{{ safe_strftime(last_backup, '%Y-%m-%d %H:%M:%S') if last_backup else 'Never' }}</td>
                                        </tr>
                                    </table>
                                    <a href="{{ url_for('backup_database') }}" class="btn btn-warning">
                                        <i class="fas fa-database"></i> Backup Database
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
