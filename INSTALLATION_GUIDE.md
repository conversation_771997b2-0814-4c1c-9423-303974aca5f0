# 🏥 Medivent Pharmaceuticals ERP System - Complete Installation Guide

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [System Requirements](#system-requirements)
3. [Quick Installation](#quick-installation)
4. [Manual Installation](#manual-installation)
5. [Dependency Analysis](#dependency-analysis)
6. [Troubleshooting](#troubleshooting)
7. [System Architecture](#system-architecture)
8. [Usage Instructions](#usage-instructions)

## 🏗️ System Overview

The Medivent Pharmaceuticals ERP System is a comprehensive web-based Enterprise Resource Planning solution built with Flask and Python. It provides complete functionality for:

- **Order Management**: Full order lifecycle from placement to delivery
- **Inventory Management**: Real-time stock tracking with FIFO allocation
- **Product Management**: Comprehensive product catalog with divisions
- **Finance Module**: Invoicing, payments, and financial reporting
- **User Management**: Role-based access control and permissions
- **Reporting & Analytics**: Advanced charts and business intelligence
- **Workflow Automation**: Automated order processing and approvals

## 💻 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.7 or higher (3.9+ recommended)
- **Memory**: 1 GB RAM minimum (2 GB recommended)
- **Storage**: 500 MB free disk space
- **Network**: Internet connection for initial setup

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 4 GB RAM
- **Storage**: 2 GB free disk space
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

## 🚀 Quick Installation

### Option 1: Enhanced One-Click Installation (Recommended)

1. **Download the ERP system** to your desired directory
2. **Open command prompt** in the ERP directory
3. **Run the enhanced installer**:
   ```batch
   install_and_run.bat
   ```
4. **The script automatically**:
   - Verifies Python 3.7+ installation
   - Kills any processes using port 3000
   - Installs all dependencies
   - Sets up the database
   - Tests the application
   - Launches the system on port 3000
5. **Access the system** at `http://localhost:3000`
6. **Login** with `admin` / `admin123`

### Option 2: Cross-Platform Installation

1. **Run the cross-platform installer**:
   ```bash
   python install_erp.py
   ```
2. **Start the system**:
   - Windows: `start_erp.bat`
   - Linux/Mac: `./start_erp.sh`
3. **Open browser** to `http://localhost:3000`
4. **Login** with `admin` / `admin123`

### Option 3: Manual Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Start the application
python run_app.py

# Open browser to http://localhost:3000
# Login: admin / admin123
```

## 🔧 Manual Installation

### Step 1: Python Installation

#### Windows
1. Download Python from [python.org](https://python.org)
2. Run installer with "Add to PATH" checked
3. Verify: `python --version`

#### macOS
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-dev
```

#### Linux (RHEL/CentOS)
```bash
sudo yum install python3 python3-pip python3-devel
```

### Step 2: System Dependencies

#### Linux (Ubuntu/Debian)
```bash
sudo apt install build-essential libsqlite3-dev libjpeg-dev libpng-dev libfreetype6-dev pkg-config
```

#### Linux (RHEL/CentOS)
```bash
sudo yum install gcc sqlite-devel libjpeg-devel libpng-devel freetype-devel
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install sqlite3 jpeg libpng freetype pkg-config
```

#### Windows
No additional system dependencies required - all handled by pip packages.

### Step 3: Python Dependencies

```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Install all required packages
pip install -r requirements.txt
```

### Step 4: Verification

```bash
# Run the verification script
python verify_installation.py
```

### Step 5: First Run

```bash
# Start the application
python run_app.py

# Or use the startup scripts
# Windows: start_erp.bat
# Linux/Mac: ./start_erp.sh
```

## 📦 Dependency Analysis

### Core Framework Dependencies
- **Flask 2.3.3**: Web framework
- **Flask-Login 0.6.2**: User authentication
- **Flask-WTF 1.2.1**: Form handling and CSRF protection
- **Werkzeug 2.3.7**: WSGI utilities
- **Jinja2 3.1.2**: Template engine

### Database Dependencies
- **SQLite3**: Built into Python (no additional installation)
- **SQLAlchemy 2.0.23**: Database ORM

### Data Processing Dependencies
- **pandas 2.2.3**: Data manipulation and analysis
- **numpy 2.2.5**: Numerical computing
- **xlsxwriter 3.1.9**: Excel file generation
- **openpyxl 3.1.2**: Excel file reading

### Visualization Dependencies
- **matplotlib 3.10.1**: Static charts and graphs
- **plotly 5.17.0**: Interactive charts
- **seaborn 0.13.0**: Statistical visualization

### Document Generation Dependencies
- **reportlab 4.0.7**: PDF generation for invoices and reports

### Image Processing Dependencies
- **Pillow 11.2.1**: Image handling and thumbnails

### Security Dependencies
- **bleach 6.1.0**: HTML sanitization and XSS prevention

### HTTP Dependencies
- **requests 2.32.3**: HTTP client for external API calls

### Frontend Dependencies (CDN-based)
- **Bootstrap 4.5.2**: UI framework
- **Font Awesome 5.15.1**: Icons
- **DataTables 1.11.5**: Table enhancements
- **Select2 4.1.0**: Enhanced dropdowns
- **Chart.js**: Client-side charts
- **jQuery**: JavaScript utilities

## 🔍 Troubleshooting

### Common Installation Issues

#### 1. Python Version Issues
**Problem**: "Python version too old"
**Solution**:
```bash
# Check current version
python --version

# Install newer Python version
# Windows: Download from python.org
# macOS: brew install python
# Linux: Use package manager or compile from source
```

#### 2. pip Installation Failures
**Problem**: "pip install failed" or "No module named pip"
**Solution**:
```bash
# Reinstall pip
python -m ensurepip --upgrade

# Or download get-pip.py
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python get-pip.py
```

#### 3. Package Compilation Errors
**Problem**: "Failed building wheel" or "Microsoft Visual C++ required"
**Solution**:

**Windows**:
- Install Microsoft Visual C++ Build Tools
- Or use pre-compiled wheels: `pip install --only-binary=all -r requirements.txt`

**Linux**:
```bash
# Install build tools
sudo apt install build-essential python3-dev
# or
sudo yum install gcc python3-devel
```

#### 4. Permission Errors
**Problem**: "Permission denied" during installation
**Solution**:
```bash
# Use user installation
pip install --user -r requirements.txt

# Or use virtual environment (recommended)
python -m venv erp_env
source erp_env/bin/activate  # Linux/Mac
# or
erp_env\Scripts\activate  # Windows
pip install -r requirements.txt
```

#### 5. Port Already in Use
**Problem**: "Port 3000 is already in use"
**Solution**:
- Kill existing process using port 3000
- Or modify `run_app.py` to use different port:
```python
app.app.run(host='0.0.0.0', port=8080, debug=True)
```

#### 6. Database Issues
**Problem**: Database errors or missing tables
**Solution**:
```bash
# Delete existing database to recreate
rm instance/medivent.db

# Restart application (will recreate database)
python run_app.py
```

### Performance Issues

#### 1. Slow Startup
- Check antivirus software (may scan Python files)
- Use SSD storage if possible
- Close unnecessary applications

#### 2. Memory Issues
- Increase system RAM
- Close other applications
- Use 64-bit Python on 64-bit systems

### Network Issues

#### 1. Cannot Access Application
- Check firewall settings
- Ensure port 3000 is not blocked
- Try accessing via `127.0.0.1:3000` instead of `localhost:3000`

#### 2. CDN Resources Not Loading
- Check internet connection
- Verify firewall allows outbound HTTPS connections
- Consider using local copies of CSS/JS files if needed

## 🏗️ System Architecture

### Application Structure
```
COMPREHENSIVE_ERP_BACKUP_20250629_015033/
├── app.py                 # Main Flask application
├── run_app.py            # Application launcher
├── requirements.txt      # Python dependencies
├── schema.sql           # Database schema
├── install_erp.py       # Installation script
├── verify_installation.py # Verification script
├── start_erp.bat        # Windows startup script
├── start_erp.sh         # Unix startup script
├── routes/              # Application routes
│   ├── auth.py         # Authentication routes
│   ├── inventory.py    # Inventory management
│   ├── orders.py       # Order management
│   ├── products.py     # Product management
│   └── users.py        # User management
├── utils/               # Utility modules
│   ├── permissions.py  # Permission system
│   ├── db.py          # Database utilities
│   └── validators/     # Data validation
├── templates/           # HTML templates
│   ├── base.html      # Base template
│   ├── dashboard/     # Dashboard templates
│   ├── orders/        # Order templates
│   └── ...
├── static/             # Static files
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   └── images/        # Images and icons
└── instance/           # Instance-specific files
    ├── medivent.db    # SQLite database
    └── uploads/       # File uploads
```

### Technology Stack
- **Backend**: Python 3.7+ with Flask framework
- **Database**: SQLite3 with SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, JavaScript with Bootstrap 4
- **Charts**: Matplotlib, Plotly, Chart.js
- **Authentication**: Flask-Login with session management
- **Security**: CSRF protection, input sanitization, role-based access

### Database Schema
The system uses SQLite with 60+ tables including:
- **Core Tables**: users, products, orders, inventory, warehouses
- **Financial Tables**: invoices, payments, customer_ledger
- **System Tables**: permissions, roles, audit_logs, settings
- **Workflow Tables**: order_workflow, inventory_allocations

## 📱 Usage Instructions

### First Time Setup
1. **Access the system**: Open `http://localhost:3000`
2. **Login**: Use `admin` / `admin123`
3. **Change password**: Go to Profile → Change Password
4. **Configure settings**: Visit Settings to customize company information
5. **Add users**: Create additional user accounts with appropriate roles
6. **Import data**: Use the data import features to load existing data

### Daily Operations
1. **Dashboard**: Monitor key metrics and recent activities
2. **Orders**: Create, process, and track customer orders
3. **Inventory**: Manage stock levels and warehouse operations
4. **Products**: Maintain product catalog and pricing
5. **Reports**: Generate business intelligence reports
6. **Finance**: Handle invoicing and payment tracking

### System Administration
1. **User Management**: Create/modify user accounts and permissions
2. **Backup**: Regular database backups (automated and manual)
3. **Monitoring**: Check system logs and performance metrics
4. **Updates**: Apply system updates and security patches

### Support and Maintenance
- **Log Files**: Check `medivent.log` for system events
- **Database Backup**: Located in `instance/` directory
- **Configuration**: Modify settings through web interface
- **Performance**: Monitor system resources and optimize as needed

---

## 📞 Support

For technical support or questions:
1. Check this documentation first
2. Review the troubleshooting section
3. Check system logs for error messages
4. Contact your system administrator

**System Version**: 1.0.0  
**Last Updated**: January 2025  
**Documentation Version**: 1.0
