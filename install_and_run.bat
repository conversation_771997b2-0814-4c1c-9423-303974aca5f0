@echo off
setlocal enabledelayedexpansion

:: Enhanced Installation and Launch Script for Medivent ERP System
:: This script performs comprehensive setup and launches the application

echo.
echo ================================================================================
echo                    MEDIVENT PHARMACEUTICALS ERP SYSTEM
echo                         ONE-CLICK INSTALLER AND LAUNCHER
echo ================================================================================
echo.
echo 🏥 Welcome to the Medivent ERP Installation Wizard
echo 📋 This script will install dependencies and launch your ERP system
echo.

:: Set color codes for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

:: Initialize variables
set "PYTHON_CMD="
set "PIP_CMD="
set "ERRORS=0"
set "PORT=3000"

echo %CYAN%🔍 STEP 1: SYSTEM VERIFICATION%RESET%
echo ----------------------------------------

:: Check if Python is installed
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set "PYTHON_CMD=python"
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set "PYTHON_VERSION=%%i"
    echo %GREEN%✅ Python found: !PYTHON_VERSION!%RESET%
) else (
    py --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_CMD=py"
        for /f "tokens=2" %%i in ('py --version 2^>^&1') do set "PYTHON_VERSION=%%i"
        echo %GREEN%✅ Python found: !PYTHON_VERSION!%RESET%
    ) else (
        echo %RED%❌ Python not found. Please install Python 3.7+ from https://python.org%RESET%
        set /a ERRORS+=1
        goto :error_exit
    )
)

:: Verify Python version (basic check)
echo Verifying Python version compatibility...
%PYTHON_CMD% -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)" >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✅ Python version is compatible (3.7+)%RESET%
) else (
    echo %RED%❌ Python version too old. Please install Python 3.7 or higher%RESET%
    set /a ERRORS+=1
    goto :error_exit
)

:: Check if pip is available
echo Checking pip installation...
%PYTHON_CMD% -m pip --version >nul 2>&1
if %errorlevel% equ 0 (
    set "PIP_CMD=%PYTHON_CMD% -m pip"
    echo %GREEN%✅ pip is available%RESET%
) else (
    echo %RED%❌ pip not found. Please ensure pip is installed%RESET%
    set /a ERRORS+=1
    goto :error_exit
)

:: Check if requirements.txt exists
echo Checking requirements file...
if exist "requirements.txt" (
    echo %GREEN%✅ requirements.txt found%RESET%
) else (
    echo %RED%❌ requirements.txt not found in current directory%RESET%
    set /a ERRORS+=1
    goto :error_exit
)

:: Check if main application file exists
echo Checking application files...
if exist "app.py" (
    echo %GREEN%✅ app.py found%RESET%
) else (
    echo %RED%❌ app.py not found in current directory%RESET%
    set /a ERRORS+=1
    goto :error_exit
)

echo.
echo %CYAN%🔧 STEP 2: PORT MANAGEMENT%RESET%
echo ----------------------------------------

:: Check if port 3000 is in use and kill processes if needed
echo Checking port %PORT% availability...
netstat -ano | findstr ":%PORT%" >nul 2>&1
if %errorlevel% equ 0 (
    echo %YELLOW%⚠️ Port %PORT% is in use. Attempting to free it...%RESET%
    
    :: Get PIDs using port 3000
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%PORT%"') do (
        set "PID=%%a"
        if not "!PID!"=="0" (
            echo Killing process !PID! using port %PORT%...
            taskkill /PID !PID! /F >nul 2>&1
            if !errorlevel! equ 0 (
                echo %GREEN%✅ Process !PID! terminated%RESET%
            ) else (
                echo %YELLOW%⚠️ Could not terminate process !PID!%RESET%
            )
        )
    )
    
    :: Wait a moment for processes to close
    timeout /t 2 >nul
    
    :: Check again
    netstat -ano | findstr ":%PORT%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %YELLOW%⚠️ Port %PORT% still in use, but continuing anyway%RESET%
    ) else (
        echo %GREEN%✅ Port %PORT% is now available%RESET%
    )
) else (
    echo %GREEN%✅ Port %PORT% is available%RESET%
)

echo.
echo %CYAN%📦 STEP 3: DEPENDENCY INSTALLATION%RESET%
echo ----------------------------------------

:: Upgrade pip first
echo Upgrading pip to latest version...
%PIP_CMD% install --upgrade pip >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✅ pip upgraded successfully%RESET%
) else (
    echo %YELLOW%⚠️ Could not upgrade pip (continuing anyway)%RESET%
)

:: Install dependencies from requirements.txt
echo Installing Python packages from requirements.txt...
echo This may take several minutes, please wait...
%PIP_CMD% install -r requirements.txt --upgrade --no-cache-dir
if %errorlevel% equ 0 (
    echo %GREEN%✅ All dependencies installed successfully%RESET%
) else (
    echo %RED%❌ Failed to install some dependencies%RESET%
    echo %YELLOW%Attempting to install critical packages individually...%RESET%
    
    :: Try installing critical packages one by one
    set "CRITICAL_PACKAGES=flask pandas matplotlib plotly reportlab pillow requests"
    for %%p in (%CRITICAL_PACKAGES%) do (
        echo Installing %%p...
        %PIP_CMD% install %%p >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%✅ %%p installed%RESET%
        ) else (
            echo %RED%❌ Failed to install %%p%RESET%
            set /a ERRORS+=1
        )
    )
)

echo.
echo %CYAN%🗄️ STEP 4: DATABASE SETUP%RESET%
echo ----------------------------------------

:: Create instance directory if it doesn't exist
if not exist "instance" (
    echo Creating instance directory...
    mkdir instance
    echo %GREEN%✅ Instance directory created%RESET%
) else (
    echo %GREEN%✅ Instance directory exists%RESET%
)

:: Check if database exists
if exist "instance\medivent.db" (
    echo %GREEN%✅ Database file exists%RESET%
) else (
    echo %YELLOW%⚠️ Database will be created on first run%RESET%
)

echo.
echo %CYAN%🧪 STEP 5: APPLICATION TESTING%RESET%
echo ----------------------------------------

:: Test if the application can be imported
echo Testing application import...
%PYTHON_CMD% -c "import app; print('✅ Application import successful')" 2>nul
if %errorlevel% equ 0 (
    echo %GREEN%✅ Application can be imported successfully%RESET%
) else (
    echo %RED%❌ Application import failed%RESET%
    echo %YELLOW%Checking for common issues...%RESET%
    
    :: Test individual imports
    %PYTHON_CMD% -c "import flask; print('Flask OK')" 2>nul || echo %RED%❌ Flask import failed%RESET%
    %PYTHON_CMD% -c "import sqlite3; print('SQLite OK')" 2>nul || echo %RED%❌ SQLite import failed%RESET%
    %PYTHON_CMD% -c "import pandas; print('Pandas OK')" 2>nul || echo %RED%❌ Pandas import failed%RESET%
    
    set /a ERRORS+=1
)

echo.
echo %CYAN%🚀 STEP 6: APPLICATION LAUNCH%RESET%
echo ----------------------------------------

if %ERRORS% gtr 0 (
    echo %RED%❌ Installation completed with %ERRORS% error(s)%RESET%
    echo %YELLOW%The application may not work correctly. Please review the errors above.%RESET%
    echo.
    echo %CYAN%🔧 TROUBLESHOOTING SUGGESTIONS:%RESET%
    echo 1. Ensure Python 3.7+ is installed
    echo 2. Check internet connection for package downloads
    echo 3. Try running as administrator
    echo 4. Manually install failed packages: pip install package_name
    echo.
    pause
    goto :end
)

echo %GREEN%🎉 INSTALLATION SUCCESSFUL!%RESET%
echo.
echo Starting Medivent ERP Application...
echo %CYAN%📱 The application will be available at: http://localhost:%PORT%%RESET%
echo %CYAN%🔐 Default login: admin / admin123%RESET%
echo.
echo %YELLOW%Press Ctrl+C to stop the application%RESET%
echo ================================================================================
echo.

:: Launch the application
%PYTHON_CMD% run_app.py

goto :end

:error_exit
echo.
echo %RED%❌ INSTALLATION FAILED%RESET%
echo.
echo %CYAN%🔧 TROUBLESHOOTING STEPS:%RESET%
echo 1. Install Python 3.7+ from https://python.org
echo 2. Ensure Python is added to PATH
echo 3. Restart command prompt after Python installation
echo 4. Run this script from the ERP system directory
echo 5. Check internet connection
echo.
echo %CYAN%📞 For support, check the INSTALLATION_GUIDE.md file%RESET%
echo.
pause
exit /b 1

:end
echo.
echo %CYAN%Thank you for using Medivent ERP System!%RESET%
pause
